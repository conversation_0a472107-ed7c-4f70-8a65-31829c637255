import cv2
import os
import argparse
import sys
import numpy as np

def create_video_from_images(image_folder, output_video=None, fps=30, extension=".png", resolution_strategy="max"):
    """
    Crée une vidéo à partir d'images PNG dans un dossier.

    Args:
        image_folder: Dossier contenant les images
        output_video: Nom du fichier vidéo de sortie (optionnel)
        fps: Images par seconde
        extension: Extension des fichiers image
        resolution_strategy: Stratégie pour la résolution ("max" ou "common")
                           - "max": utilise la résolution maximale trouvée
                           - "common": utilise la résolution la plus commune
    """
    # Définir le nom de sortie par défaut si non spécifié
    if output_video is None:
        folder_name = os.path.basename(image_folder.rstrip("\\/"))
        output_video = os.path.join(image_folder, f"{folder_name}_video.mp4")

    print(f"[INFO] Création de vidéo depuis: {image_folder}")
    print(f"[INFO] Fichier de sortie: {output_video}")
    print(f"[INFO] FPS: {fps}, Extension: {extension}")

    # Récupérer et trier les fichiers image
    images = [img for img in os.listdir(image_folder) if img.endswith(extension)]
    images.sort()

    if not images:
        raise ValueError("Aucune image trouvée dans le dossier.")

    # Analyser toutes les images pour déterminer la résolution optimale
    print(f"[INFO] Analyse des dimensions de {len(images)} images...")
    dimensions = []

    for image in images:
        img_path = os.path.join(image_folder, image)
        frame = cv2.imread(img_path)
        if frame is not None:
            h, w = frame.shape[:2]
            dimensions.append((w, h))
        else:
            print(f"[WARNING] Impossible de lire l'image: {img_path}")

    if not dimensions:
        raise ValueError("Aucune image valide trouvée.")

    # Déterminer la résolution optimale (la plus grande largeur et hauteur)
    max_width = max(dim[0] for dim in dimensions)
    max_height = max(dim[1] for dim in dimensions)

    # Optionnel: utiliser la résolution la plus commune au lieu de la plus grande
    # Compter les occurrences de chaque dimension
    from collections import Counter
    dimension_counts = Counter(dimensions)
    most_common_dim = dimension_counts.most_common(1)[0][0]

    print(f"[INFO] Dimensions trouvées: {len(set(dimensions))} résolutions différentes")
    print(f"[INFO] Résolution maximale: {max_width}x{max_height}")
    print(f"[INFO] Résolution la plus commune: {most_common_dim[0]}x{most_common_dim[1]} ({dimension_counts[most_common_dim]} images)")

    # Choisir la stratégie selon le paramètre
    if resolution_strategy == "max":
        width, height = max_width, max_height
        print(f"[INFO] Utilisation de la résolution maximale: {width}x{height}")
    elif resolution_strategy == "common":
        width, height = most_common_dim
        print(f"[INFO] Utilisation de la résolution la plus commune: {width}x{height}")
    else:
        raise ValueError(f"Stratégie de résolution inconnue: {resolution_strategy}. Utilisez 'max' ou 'common'.")

    # Créer l'objet VideoWriter
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')  # Codec pour mp4
    video = cv2.VideoWriter(output_video, fourcc, fps, (width, height))

    # Ajouter les images à la vidéo
    print(f"[INFO] Création de la vidéo avec {len(images)} images...")

    for i, image in enumerate(images):
        img_path = os.path.join(image_folder, image)
        frame = cv2.imread(img_path)

        if frame is None:
            print(f"[WARNING] Impossible de lire l'image: {img_path}")
            continue

        current_height, current_width = frame.shape[:2]

        # Adapter l'image à la résolution de la vidéo
        if current_height != height or current_width != width:
            # Créer un canvas noir de la taille de la vidéo
            canvas = np.zeros((height, width, 3), dtype=np.uint8)

            # Calculer le ratio pour préserver les proportions
            ratio_w = width / current_width
            ratio_h = height / current_height
            ratio = min(ratio_w, ratio_h)  # Utiliser le plus petit ratio pour que l'image tienne entièrement

            # Nouvelles dimensions en préservant les proportions
            new_width = int(current_width * ratio)
            new_height = int(current_height * ratio)

            # Redimensionner l'image
            resized_frame = cv2.resize(frame, (new_width, new_height))

            # Centrer l'image sur le canvas
            start_x = (width - new_width) // 2
            start_y = (height - new_height) // 2
            canvas[start_y:start_y + new_height, start_x:start_x + new_width] = resized_frame

            frame = canvas

            if i == 0 or (i + 1) % 50 == 0:  # Afficher moins souvent pour éviter le spam
                print(f"[INFO] {image}: {current_width}x{current_height} -> centré dans {width}x{height}")

        video.write(frame)

        # Afficher le progrès
        if (i + 1) % 10 == 0 or i == 0:
            print(f"[PROGRESS] Traitement: {i + 1}/{len(images)} images")

    video.release()
    print(f"[SUCCESS] Vidéo créée : {output_video}")
    return output_video

def main():
    """Fonction principale avec gestion des arguments"""
    parser = argparse.ArgumentParser(description="Créer une vidéo à partir d'images PNG")
    parser.add_argument(
        "image_folder",
        help="Dossier contenant les images PNG"
    )
    parser.add_argument(
        "--output",
        help="Nom du fichier vidéo de sortie (optionnel, par défaut dans le dossier d'images)"
    )
    parser.add_argument(
        "--fps",
        type=int,
        default=30,
        help="Images par seconde (défaut: 30)"
    )
    parser.add_argument(
        "--extension",
        default=".png",
        help="Extension des fichiers image (défaut: .png)"
    )
    parser.add_argument(
        "--resolution",
        choices=["max", "common"],
        default="max",
        help="Stratégie de résolution: 'max' pour la résolution maximale, 'common' pour la plus commune (défaut: max)"
    )

    args = parser.parse_args()

    try:
        # Vérifier que le dossier existe
        if not os.path.exists(args.image_folder):
            print(f"[ERROR] Le dossier {args.image_folder} n'existe pas")
            sys.exit(1)

        # Créer la vidéo
        output_video = create_video_from_images(
            image_folder=args.image_folder,
            output_video=args.output,
            fps=args.fps,
            extension=args.extension,
            resolution_strategy=args.resolution
        )

        print(f"[SUCCESS] Vidéo créée avec succès: {output_video}")

    except Exception as e:
        print(f"[ERROR] Erreur lors de la création de la vidéo: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
