# Script PowerShell pour creer la structure nnU-Net avec chemins personnalises
# Executer : .\new_path_windows.ps1
# Modifier les chemins ci-dessous selon vos besoins

# Definition des chemins personnalises - MODIFIEZ CES CHEMINS
$RAW_PATH = "C:\Users\<USER>\Documents\datasets\nnUnet\nnUnet_raw"
$PREPROCESSED_PATH = "C:\Users\<USER>\Documents\datasets\nnUnet\nnUnet_preprocessed"
$RESULTS_PATH = "C:\Users\<USER>\Documents\results\nnUnet_results"

Write-Host "Creation des dossiers..." -ForegroundColor Cyan

# Creation des dossiers
try {
    New-Item -ItemType Directory -Path $RAW_PATH -Force | Out-Null
    New-Item -ItemType Directory -Path $PREPROCESSED_PATH -Force | Out-Null
    New-Item -ItemType Directory -Path $RESULTS_PATH -Force | Out-Null

    Write-Host "Structure creee avec succes :" -ForegroundColor Green
    Write-Host "  - $RAW_PATH" -ForegroundColor White
    Write-Host "  - $PREPROCESSED_PATH" -ForegroundColor White
    Write-Host "  - $RESULTS_PATH" -ForegroundColor White

    Write-Host "Dossiers crees avec succes aux emplacements specifies" -ForegroundColor Green

}
catch {
    Write-Host "Erreur lors de la creation : $($_.Exception.Message)" -ForegroundColor Red
    Read-Host "Appuyez sur Entree pour fermer"
    exit 1
}

# Affichage des variables d'environnement a definir
Write-Host ""
Write-Host "Variables d'environnement a definir :" -ForegroundColor Yellow
Write-Host "nnUNet_raw_data_base=$RAW_PATH" -ForegroundColor White
Write-Host "nnUNet_preprocessed=$PREPROCESSED_PATH" -ForegroundColor White
Write-Host "RESULTS_FOLDER=$RESULTS_PATH" -ForegroundColor White

Write-Host ""
Write-Host "Pour definir les variables d'environnement automatiquement, executez :" -ForegroundColor Yellow
Write-Host "[Environment]::SetEnvironmentVariable('nnUNet_raw_data_base', '$RAW_PATH', 'User')" -ForegroundColor Cyan
Write-Host "[Environment]::SetEnvironmentVariable('nnUNet_preprocessed', '$PREPROCESSED_PATH', 'User')" -ForegroundColor Cyan
Write-Host "[Environment]::SetEnvironmentVariable('RESULTS_FOLDER', '$RESULTS_PATH', 'User')" -ForegroundColor Cyan

Write-Host ""
Write-Host "Script termine avec succes !" -ForegroundColor Green
Read-Host "Appuyez sur Entree pour fermer"
