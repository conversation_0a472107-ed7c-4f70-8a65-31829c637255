import os
import subprocess
import time
import logging
import platform
from pathlib import Path

# === CONFIGURATION (MODIFIEZ ICI) ===
DATASET_ID = "011"
CONFIGURATION = "2d"      # '2d', '3d_fullres', '3d_lowres'
PLANS_NAME = "nnUNetPlans"
EPOCHS = 5
GPU_ID = "0"              # "0" ou "0,1,2,3" pour multi-GPU
NUM_GPUS = 1

# === VALIDATION CROISÉE ===
USE_CROSS_VALIDATION = True  # True = 5-fold CV, False = mode original "all"
VALIDATION_FOLDS = [0, 1, 2, 3, 4, "all"]  # Folds à entraîner - peut inclure "all" si USE_CROSS_VALIDATION = True

# === HYPERPARAMÈTRES ===
RUN_PREPROCESSING = True  # True = exécute le prétraitement, False = ignore le prétraitement
SAVE_NPZ = False
CONTINUE_TRAINING = False

# === DÉTECTION SYSTÈME ET PATHS ===
# Détection automatique du système d'exploitation
IS_WINDOWS = platform.system() == "Windows"

# Configuration des chemins selon le système
if IS_WINDOWS:
    # Chemins Windows
    RAW_PATH = r"C:\Users\<USER>\Documents\datasets\nnUnet\nnUnet_raw"
    PREPROCESSED_PATH = r"C:\Users\<USER>\Documents\datasets\nnUnet\nnUnet_preprocessed"
    RESULTS_PATH = r"C:\Users\<USER>\Documents\results\nnUnet_results"
else:
    # Chemins Linux/Unix
    RAW_PATH = "/mnt/datasets/nnUnet/nnUnet_raw"
    PREPROCESSED_PATH = "/mnt/datasets/nnUnet/nnUnet_preprocessed"
    RESULTS_PATH = "/mnt/results/nnUnet_results"

# === CONFIGURATION ENVIRONNEMENT ===
os.environ["nnUNet_raw"] = RAW_PATH
os.environ["nnUNet_preprocessed"] = PREPROCESSED_PATH
os.environ["nnUNet_results"] = RESULTS_PATH
os.environ["CUDA_VISIBLE_DEVICES"] = GPU_ID
os.environ["nnUNet_n_proc_DA"] = "2"

# Fix OpenMP library conflict
os.environ["KMP_DUPLICATE_LIB_OK"] = "TRUE"

# Reduce number of processes to avoid memory issues
os.environ["nnUNet_def_n_proc"] = "1"

# === LOGGING SIMPLE ===
def setup_logging():
    """Configure le logging simple"""
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)

    timestamp = time.strftime("%Y%m%d_%H%M%S")
    log_file = log_dir / f"nnunet_training_{DATASET_ID}_{timestamp}.log"

    # Configure file handler with UTF-8 encoding
    file_handler = logging.FileHandler(log_file, encoding='utf-8')
    file_handler.setLevel(logging.INFO)

    # Configure console handler with UTF-8 encoding and error handling
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)

    # Set formatter
    formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
    file_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)

    # Configure root logger
    logger = logging.getLogger(__name__)
    logger.setLevel(logging.INFO)
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)

    return logger

logger = setup_logging()

def run_command(cmd: str, description: str = "") -> bool:
    """Exécute une commande avec gestion d'erreurs"""
    logger.info(f"[START] {description if description else 'Lancement'}: {cmd}")

    try:
        subprocess.run(cmd, shell=True, check=True)
        logger.info(f"[SUCCESS] Succès: {description}")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"[ERROR] Erreur lors de: {description}")
        logger.error(f"Code de retour: {e.returncode}")
        return False





def run_preprocessing() -> bool:
    """Exécute le prétraitement nnUNet"""
    # Use single process to avoid memory issues and worker failures
    cmd = f'nnUNetv2_plan_and_preprocess -d {DATASET_ID} --verify_dataset_integrity -np 1'
    return run_command(cmd, "Prétraitement et vérification du dataset")

def train_single_fold(fold) -> bool:
    """Entraîne un seul fold"""
    # Logique pour le nom du trainer selon le nombre d'époques
    if EPOCHS == 1:
        trainer_class = "nnUNetTrainer_1epoch"
    else:
        trainer_class = f"nnUNetTrainer_{EPOCHS}epochs"

    train_cmd = f'nnUNetv2_train {DATASET_ID} {CONFIGURATION} {fold} -p {PLANS_NAME} -tr {trainer_class}'

    if SAVE_NPZ:
        train_cmd += " --npz"
    if CONTINUE_TRAINING:
        train_cmd += " --c"
    if NUM_GPUS > 1:
        train_cmd += f" -num_gpus {NUM_GPUS}"

    success = run_command(train_cmd, f"Entraînement fold {fold}")

    if success:
        logger.info(f"[SUCCESS] Fold {fold} terminé avec succès")
    else:
        logger.error(f"[ERROR] Échec du fold {fold}")

    return success

def run_cross_validation() -> bool:
    """Exécute la validation croisée - peut inclure des folds numériques et/ou 'all'"""
    successful_folds = []
    failed_folds = []

    # Affichage informatif selon le contenu de VALIDATION_FOLDS
    if "all" in VALIDATION_FOLDS:
        numeric_folds = [f for f in VALIDATION_FOLDS if f != "all"]
        if numeric_folds:
            logger.info(f"[CV] Mode mixte - folds individuels: {numeric_folds} + fold 'all'")
        else:
            logger.info(f"[CV] Mode 'all' uniquement")
    else:
        logger.info(f"[CV] Validation croisée sur {len(VALIDATION_FOLDS)} folds: {VALIDATION_FOLDS}")

    # Entraînement séquentiel des folds
    for fold in VALIDATION_FOLDS:
        logger.info(f"[FOLD] Début du fold {fold}")
        success = train_single_fold(fold)

        if success:
            successful_folds.append(fold)
        else:
            failed_folds.append(fold)

    # Résumé des résultats
    logger.info(f"[RESULTS] Résultats de l'entraînement:")
    logger.info(f"[SUCCESS] Folds réussis: {successful_folds}")
    if failed_folds:
        logger.warning(f"[ERROR] Folds échoués: {failed_folds}")

    return len(failed_folds) == 0

def find_best_configuration() -> bool:
    """Trouve automatiquement la meilleure configuration et crée les fichiers d'ensemble"""
    # Logique pour le nom du trainer selon le nombre d'époques
    if EPOCHS == 1:
        trainer_class = "nnUNetTrainer_1epoch"
    else:
        trainer_class = f"nnUNetTrainer_{EPOCHS}epochs"

    cmd = f'nnUNetv2_find_best_configuration {DATASET_ID} -c {CONFIGURATION} -tr {trainer_class}'
    success = run_command(cmd, "Recherche de la meilleure configuration et création de l'ensemble")

    if success:
        logger.info("[SUCCESS] Configuration optimale trouvée !")
        logger.info("[FILES] Fichiers générés :")
        logger.info(f"   - inference_instructions.txt")
        logger.info(f"   - inference_information.json")
        logger.info(f"   - postprocessing.pkl")
        logger.info("[INFO] Consultez inference_instructions.txt pour les commandes d'inférence")
    else:
        logger.error("[ERROR] Échec de la recherche de configuration")

    return success

def main():
    """Fonction principale"""
    start_time = time.time()

    try:
        logger.info("[START] Début de l'entraînement nnUNet")
        logger.info(f"[CONFIG] Configuration:")
        logger.info(f"   - Dataset ID: {DATASET_ID}")
        logger.info(f"   - Configuration: {CONFIGURATION}")
        logger.info(f"   - Époques: {EPOCHS}")
        logger.info(f"   - Validation croisée: {USE_CROSS_VALIDATION}")
        logger.info(f"   - Prétraitement: {RUN_PREPROCESSING}")
        logger.info(f"   - GPU(s): {GPU_ID}")
        # Affichage du trainer utilisé
        if EPOCHS == 1:
            trainer_name = "nnUNetTrainer_1epoch"
        else:
            trainer_name = f"nnUNetTrainer_{EPOCHS}epochs"
        logger.info(f"   - Trainer: {trainer_name}")

        # 1. Validation des noms de fichiers - SUPPRIMÉE

        # 3. Prétraitement (conditionnel)
        if RUN_PREPROCESSING:
            logger.info("[PREPROCESSING] Lancement du prétraitement...")
            if not run_preprocessing():
                logger.error("[ERROR] Échec du prétraitement")
                return False
        else:
            logger.info("[PREPROCESSING] Prétraitement ignoré (RUN_PREPROCESSING = False)")

        # 4. Entraînement
        if USE_CROSS_VALIDATION:
            success = run_cross_validation()
        else:
            # Entraînement sur toutes les données (mode original)
            logger.warning("[WARNING] Mode 'all' - pas de vraie validation!")
            success = train_single_fold("all")

        if not success:
            logger.error("[ERROR] Échec de l'entraînement")
            return False

        # 5. Recherche de la meilleure configuration (seulement si validation croisée ET SAVE_NPZ activé)
        if USE_CROSS_VALIDATION and SAVE_NPZ:
            logger.info("[SEARCH] Recherche de la meilleure configuration...")
            config_success = find_best_configuration()
            if not config_success:
                logger.warning("[WARNING] Échec de la recherche de configuration, mais entraînement réussi")
        elif USE_CROSS_VALIDATION and not SAVE_NPZ:
            logger.info("[INFO] Recherche de configuration ignorée (SAVE_NPZ désactivé)")
        else:
            logger.info("[INFO] Recherche de configuration ignorée (pas de validation croisée)")

        # Temps total
        total_time = time.time() - start_time
        logger.info(f"[COMPLETE] Processus complet terminé en {total_time/3600:.2f} heures")

        return True

    except Exception as e:
        logger.error(f"[FATAL] Erreur fatale: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
