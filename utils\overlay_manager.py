import os
import numpy as np
import cv2
import logging
from typing import Tuple, Optional
from skimage.transform import resize

# Configuration du logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class OverlayManager:
    """
    Gère la superposition des masques sur les images.
    """
    
    def __init__(self):
        """Initialise le gestionnaire d'overlay."""
        self._logger = logger
        # Définition des couleurs pour chaque classe (format RGB normalisé 0-1)
        # Couleurs CORRIGÉES avec rouge et vert inversés pour correspondre à la sémantique
        self.class_colors = {
            0: [0, 0, 0],        # background (noir)
            29: [0, 0.9, 1],     # frontwall (cyan brillant)
            149: [0.1, 1, 0.1],  # backwall (VERT) - CORRIGÉ
            76: [1, 0.1, 0.1],   # flaw (ROUGE) - CORRIGÉ
            125: [1, 1, 0.1]     # indication (jaune pur)
        }

        # Noms des classes pour le debug (avec couleurs corrigées)
        self.class_names = {
            0: "background",
            29: "frontwall (cyan)",
            149: "backwall (VERT - corrigé)",
            76: "flaw (ROUGE - corrigé)",
            125: "indication (jaune)"
        }

    def create_overlay(
        self,
        original_image: np.ndarray,
        mask_image: np.ndarray,
        alpha: float = 0.4,
        enhance_colors: bool = True,
        darken_background: float = 0.6
    ) -> np.ndarray:
        """
        Crée une superposition de l'image originale et du masque.

        Args:
            original_image: Image originale en format BGR
            mask_image: Image du masque (binaire ou RGB)
            alpha: Transparence du masque (0-1)
            enhance_colors: Si True, améliore la visibilité des couleurs
            darken_background: Facteur d'assombrissement de l'arrière-plan (0-1)

        Returns:
            numpy.ndarray: Image avec overlay
        """
        try:
            self._logger.info(f"Création de l'overlay avec alpha={alpha}")
            self._logger.info(f"Type de l'image originale: {original_image.dtype}, forme: {original_image.shape}")
            self._logger.info(f"Type du masque: {mask_image.dtype}, forme: {mask_image.shape}")

            # Convertir l'image originale en RGB si nécessaire
            if original_image.ndim == 2:
                original_image = np.stack([original_image] * 3, axis=-1)
            elif original_image.shape[2] == 3:
                original_image = cv2.cvtColor(original_image, cv2.COLOR_BGR2RGB)

            # Convertir le masque en RGB si nécessaire
            if mask_image.ndim == 2:
                unique_vals = np.unique(mask_image)
                self._logger.info(f"Valeurs uniques dans le masque: {unique_vals}")

                # Créer un masque coloré
                color_mask = np.zeros((*mask_image.shape, 3))

                # Appliquer les couleurs pour chaque classe
                for class_value, color in self.class_colors.items():
                    mask_class = (mask_image == class_value)
                    pixel_count = np.sum(mask_class)
                    if pixel_count > 0:
                        color_mask[mask_class] = color
                        class_name = self.class_names.get(class_value, f"classe_{class_value}")
                        self._logger.info(f"✅ {class_name} (valeur {class_value}): {pixel_count} pixels - couleur RGB{color}")
                    else:
                        class_name = self.class_names.get(class_value, f"classe_{class_value}")
                        self._logger.info(f"⚪ {class_name} (valeur {class_value}): 0 pixels")

                # Vérifier si des valeurs du masque ne sont pas mappées
                for val in unique_vals:
                    if val not in self.class_colors:
                        self._logger.warning(f"⚠️ Valeur {val} trouvée dans le masque mais pas de couleur définie!")

                mask_image = color_mask

            # Redimensionner le masque si nécessaire
            if mask_image.shape[:2] != original_image.shape[:2]:
                self._logger.warning("Redimensionnement du masque pour correspondre à l'image originale")
                mask_image = resize(mask_image, original_image.shape[:2], preserve_range=True, anti_aliasing=False)

            # Normalisation
            if original_image.dtype == np.uint8:
                original_image = original_image.astype(float) / 255.0
            if mask_image.dtype == np.uint8:
                mask_image = mask_image.astype(float) / 255.0

            # Créer l'overlay avec une visibilité accrue du masque
            mask_alpha = (mask_image > 0).any(axis=-1, keepdims=True)

            # Créer l'overlay avec une meilleure visibilité des couleurs
            overlay = original_image.copy()

            # Pour chaque pixel où il y a un masque, mélanger les couleurs
            mask_pixels = mask_alpha.squeeze()
            if np.any(mask_pixels):
                # Assombrir l'image de base sous le masque pour plus de contraste
                overlay[mask_pixels] = overlay[mask_pixels] * darken_background

                # Ajouter les couleurs du masque
                overlay[mask_pixels] = (overlay[mask_pixels] * (1 - alpha) +
                                      mask_image[mask_pixels] * alpha)

                # Si enhance_colors est activé, augmenter la saturation
                if enhance_colors:
                    # Augmenter la contribution des couleurs du masque
                    mask_contribution = mask_image[mask_pixels] * 0.4
                    overlay[mask_pixels] = overlay[mask_pixels] + mask_contribution

            overlay = np.clip(overlay, 0, 1)
            
            self._logger.info("Overlay créé avec succès")
            return overlay

        except Exception as e:
            self._logger.error(f"Erreur lors de la création de l'overlay: {str(e)}")
            raise

    def create_high_contrast_overlay(
        self,
        original_image: np.ndarray,
        mask_image: np.ndarray,
        alpha: float = 0.7
    ) -> np.ndarray:
        """
        Crée un overlay avec un contraste élevé pour une meilleure visibilité des couleurs.

        Args:
            original_image: Image originale en format BGR
            mask_image: Image du masque (binaire ou RGB)
            alpha: Transparence du masque (0-1)

        Returns:
            numpy.ndarray: Image avec overlay haute visibilité
        """
        try:
            self._logger.info(f"Création de l'overlay haute visibilité avec alpha={alpha}")

            # Convertir l'image originale en RGB si nécessaire
            if original_image.ndim == 2:
                original_image = np.stack([original_image] * 3, axis=-1)
            elif original_image.shape[2] == 3:
                original_image = cv2.cvtColor(original_image, cv2.COLOR_BGR2RGB)

            # Convertir le masque en RGB si nécessaire
            if mask_image.ndim == 2:
                unique_vals = np.unique(mask_image)
                self._logger.info(f"Valeurs uniques dans le masque: {unique_vals}")

                # Créer un masque coloré avec des couleurs plus saturées
                color_mask = np.zeros((*mask_image.shape, 3))

                # CORRECTION: Utiliser les couleurs corrigées de la classe principale
                # au lieu d'un mapping séparé qui causait l'inversion

                # Appliquer les couleurs pour chaque classe
                for class_value, color in self.class_colors.items():
                    mask_class = (mask_image == class_value)
                    pixel_count = np.sum(mask_class)
                    if pixel_count > 0:
                        color_mask[mask_class] = color
                        class_name = self.class_names.get(class_value, f"classe_{class_value}")
                        self._logger.info(f"✅ {class_name} (valeur {class_value}): {pixel_count} pixels")

                mask_image = color_mask

            # Redimensionner le masque si nécessaire
            if mask_image.shape[:2] != original_image.shape[:2]:
                self._logger.warning("Redimensionnement du masque pour correspondre à l'image originale")
                mask_image = resize(mask_image, original_image.shape[:2], preserve_range=True, anti_aliasing=False)

            # Normalisation
            if original_image.dtype == np.uint8:
                original_image = original_image.astype(float) / 255.0
            if mask_image.dtype == np.uint8:
                mask_image = mask_image.astype(float) / 255.0

            # Créer l'overlay avec méthode haute visibilité
            mask_alpha = (mask_image > 0).any(axis=-1, keepdims=True)

            # Méthode alternative: overlay additif pour plus de visibilité
            overlay = original_image.copy()

            # Convertir l'image en niveaux de gris pour l'arrière-plan
            gray_bg = np.mean(original_image, axis=-1, keepdims=True)
            gray_bg = np.repeat(gray_bg, 3, axis=-1) * 0.4  # Assombrir l'arrière-plan

            # Appliquer l'arrière-plan gris où il y a un masque
            overlay = np.where(mask_alpha, gray_bg, original_image)

            # Ajouter les couleurs du masque de manière additive
            overlay = overlay + mask_image * alpha

            overlay = np.clip(overlay, 0, 1)

            self._logger.info("Overlay haute visibilité créé avec succès")
            return overlay

        except Exception as e:
            self._logger.error(f"Erreur lors de la création de l'overlay haute visibilité: {str(e)}")
            raise

    def create_contour_overlay(
        self,
        original_image: np.ndarray,
        mask_image: np.ndarray,
        contour_thickness: int = 2,
        use_class_colors: bool = True
    ) -> np.ndarray:
        """
        Crée un overlay avec seulement les contours du masque sur l'image originale.

        Args:
            original_image: Image originale en format BGR
            mask_image: Masque en niveaux de gris
            contour_thickness: Épaisseur des contours
            use_class_colors: Si True, utilise les couleurs de classe définies

        Returns:
            numpy.ndarray: Image overlay avec contours colorés (format RGB)
        """
        try:
            self._logger.info(f"Création de l'overlay contours avec épaisseur={contour_thickness}")

            # Convertir l'image originale en RGB si nécessaire
            if original_image.ndim == 2:
                original_image = np.stack([original_image] * 3, axis=-1)
            elif original_image.shape[2] == 3:
                original_image = cv2.cvtColor(original_image, cv2.COLOR_BGR2RGB)

            # Copier l'image originale comme base
            overlay = original_image.copy()

            # Convertir en uint8 si nécessaire pour OpenCV
            if overlay.dtype != np.uint8:
                overlay = (overlay * 255).astype(np.uint8)

            # Définir les couleurs BGR pour OpenCV (inversées par rapport à RGB)
            if use_class_colors:
                # Convertir les couleurs RGB en BGR et en format uint8
                bgr_colors = {}
                for class_value, rgb_color in self.class_colors.items():
                    if class_value != 0:  # Ignorer le background
                        # Convertir RGB [0-1] vers BGR [0-255]
                        bgr_color = (
                            int(rgb_color[2] * 255),  # B
                            int(rgb_color[1] * 255),  # G
                            int(rgb_color[0] * 255)   # R
                        )
                        bgr_colors[class_value] = bgr_color
            else:
                # Couleurs par défaut
                bgr_colors = {
                    29: (255, 255, 0),    # Cyan
                    149: (0, 255, 0),     # Vert
                    76: (0, 0, 255),      # Rouge
                    125: (0, 255, 255)    # Jaune
                }

            # Pour chaque classe, trouver et dessiner les contours
            unique_vals = np.unique(mask_image)
            self._logger.info(f"Valeurs uniques dans le masque: {unique_vals}")

            for class_value in unique_vals:
                if class_value == 0:  # Ignorer le background
                    continue

                if class_value in bgr_colors:
                    color = bgr_colors[class_value]

                    # Créer un masque binaire pour cette classe
                    class_mask = (mask_image == class_value).astype('uint8') * 255

                    # Trouver les contours
                    contours, _ = cv2.findContours(class_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

                    if len(contours) > 0:
                        # Dessiner les contours sur l'overlay
                        cv2.drawContours(overlay, contours, -1, color, contour_thickness)

                        class_name = self.class_names.get(class_value, f"classe_{class_value}")
                        self._logger.info(f"✅ Contours dessinés pour {class_name} (valeur {class_value}): {len(contours)} contours")
                else:
                    self._logger.warning(f"⚠️ Pas de couleur définie pour la classe {class_value}")

            # Convertir de BGR vers RGB pour la sortie
            overlay_rgb = cv2.cvtColor(overlay, cv2.COLOR_BGR2RGB)

            self._logger.info("Overlay contours créé avec succès")
            return overlay_rgb

        except Exception as e:
            self._logger.error(f"Erreur lors de la création de l'overlay contours: {str(e)}")
            raise

    def save_overlay(
        self,
        overlay: np.ndarray,
        output_path: str
    ) -> None:
        """
        Sauvegarde l'overlay dans un fichier avec la méthode corrigée.

        Args:
            overlay: Image avec overlay
            output_path: Chemin de sauvegarde
        """
        try:
            # Créer le dossier de sortie si nécessaire
            output_dir = os.path.dirname(output_path)
            if output_dir:  # Vérifier que le dossier n'est pas vide
                os.makedirs(output_dir, exist_ok=True)

            # CORRECTION: Utiliser PIL au lieu de matplotlib pour préserver l'ordre RGB
            # Convertir en uint8 et s'assurer que les valeurs sont dans [0, 255]
            if overlay.dtype == np.float64 or overlay.dtype == np.float32:
                overlay_uint8 = (np.clip(overlay, 0, 1) * 255).astype(np.uint8)
            else:
                overlay_uint8 = overlay.astype(np.uint8)

            # Sauvegarder avec PIL pour préserver l'ordre RGB correct
            from PIL import Image
            overlay_pil = Image.fromarray(overlay_uint8, 'RGB')
            overlay_pil.save(output_path)

            self._logger.info(f"Overlay sauvegardé (méthode corrigée): {output_path}")

        except Exception as e:
            self._logger.error(f"Erreur lors de la sauvegarde de l'overlay: {str(e)}")
            raise