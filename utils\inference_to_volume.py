#!/usr/bin/env python3
"""
Service pour convertir les résultats d'inférence nnUNet (masques PNG) en volume 3D.
Adapté spécifiquement pour traiter les sorties d'inférence et les sauvegarder dans différents formats.
"""
import os
import numpy as np
from glob import glob
from PIL import Image
import argparse
import sys
from pathlib import Path
import logging
import re

# ============================================================================
# CONFIGURATION HARDCODÉE - Modifiez ces valeurs selon vos besoins
# ============================================================================

# Ordre des axes du volume final
# 'ZYX': (profondeur, hauteur, largeur) - défaut pour nnUNet
# 'ZXY': (profondeur, largeur, hauteur)
# 'YZX': (hauteur, profondeur, largeur)
# 'YXZ': (hauteur, largeur, profondeur)
# 'XZY': (largeur, profondeur, hauteur)
# 'XYZ': (largeur, hauteur, profondeur)
AXIS_ORDER = 'ZXY'

# Format de sortie du volume
# 'npz': Format NumPy compressé (recommandé)
# 'h5': Format HDF5
# 'nifti': Format NIfTI médical
OUTPUT_FORMAT = 'npz'

# Mode couleur des images (pour les masques d'inférence, toujours grayscale)
COLOR_MODE = 'L'

# Nom de la clé dans le fichier NPZ
# 'inference_masks': Nom descriptif pour les masques d'inférence
# 'arr_0': Format Sentinel standard
# 'volume': Format classique
ARRAY_KEY_NAME = 'arr_0'

# Préfixe pour les fichiers de sortie
OUTPUT_PREFIX = 'segmentation_mask'

# ============================================================================

class InferenceToVolumeConverter:
    def __init__(self, inference_folder, output_path, output_format='npz', axis_order='ZYX'):
        """
        Initialise le convertisseur de masques d'inférence vers volume

        Args:
            inference_folder: Dossier contenant les masques d'inférence PNG
            output_path: Chemin de sortie (sans extension)
            output_format: Format de sortie ('npz', 'h5', 'nifti')
            axis_order: Ordre des axes ('ZYX', 'XYZ', etc.)
        """
        self.inference_folder = Path(inference_folder)
        self.output_path = Path(output_path)
        self.output_format = output_format.lower()
        self.axis_order = axis_order.upper()

        # Vérifications
        if not self.inference_folder.exists():
            raise FileNotFoundError(f"Le dossier {inference_folder} n'existe pas")

        if self.output_format not in ['npz', 'h5', 'nifti']:
            raise ValueError("Format de sortie doit être 'npz', 'h5' ou 'nifti'")

        # Configuration du logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)

    def convert(self):
        """Effectue la conversion complète"""
        self.logger.info(f"[INFERENCE_TO_VOLUME] Conversion des masques d'inférence vers volume")
        self.logger.info(f"[INFERENCE_TO_VOLUME] Dossier source : {self.inference_folder}")
        self.logger.info(f"[INFERENCE_TO_VOLUME] Fichier sortie : {self.output_path}")
        self.logger.info(f"[INFERENCE_TO_VOLUME] Format : {self.output_format.upper()}")
        self.logger.info(f"[INFERENCE_TO_VOLUME] Ordre des axes : {self.axis_order}")

        # Charger les masques d'inférence
        volume = self.load_inference_masks()

        # Réorganiser les axes si nécessaire
        volume = self.reorder_axes(volume)

        # Sauvegarder
        self.save_volume(volume)

        self.logger.info(f"[INFERENCE_TO_VOLUME] ✅ Conversion terminée avec succès !")
        return True

    def load_inference_masks(self):
        """Charge tous les masques d'inférence PNG, triés numériquement, et les empile en un volume 3D."""
        # Chercher les fichiers PNG dans le dossier d'inférence
        png_files = list(self.inference_folder.glob('*.png'))
        
        if not png_files:
            raise FileNotFoundError(f"Aucun masque PNG trouvé dans {self.inference_folder}")

        # Trier les fichiers numériquement (important pour l'ordre des slices)
        def extract_number(filename):
            """Extrait le numéro du fichier pour le tri numérique"""
            match = re.search(r'(\d+)', filename.stem)
            return int(match.group(1)) if match else 0

        png_files.sort(key=extract_number)
        
        self.logger.info(f"[INFERENCE_TO_VOLUME] {len(png_files)} masques d'inférence trouvés")
        self.logger.info(f"[INFERENCE_TO_VOLUME] Premier fichier : {png_files[0].name}")
        self.logger.info(f"[INFERENCE_TO_VOLUME] Dernier fichier : {png_files[-1].name}")

        slices = []
        for i, png_file in enumerate(png_files):
            if i % 50 == 0:  # Afficher le progrès
                self.logger.info(f"[INFERENCE_TO_VOLUME] Chargement : {i+1}/{len(png_files)} ({(i+1)/len(png_files)*100:.1f}%)")

            # Charger le masque en niveaux de gris
            img = Image.open(png_file).convert(COLOR_MODE)
            mask_array = np.array(img)
            slices.append(mask_array)

        volume = np.stack(slices, axis=0)
        
        # Analyser les valeurs uniques dans le volume (classes de segmentation)
        unique_values = np.unique(volume)
        self.logger.info(f"[INFERENCE_TO_VOLUME] Volume créé : {volume.shape} ({self.axis_order})")
        self.logger.info(f"[INFERENCE_TO_VOLUME] Type de données : {volume.dtype}")
        self.logger.info(f"[INFERENCE_TO_VOLUME] Taille mémoire : {volume.nbytes / (1024*1024):.2f} MB")
        self.logger.info(f"[INFERENCE_TO_VOLUME] Classes détectées : {unique_values}")

        return volume

    def reorder_axes(self, volume):
        """Réorganise les axes selon l'ordre spécifié

        Volume original : (Z, Y, X) où Z=nombre d'images, Y=hauteur, X=largeur
        """
        # Dictionnaire des transpositions possibles
        transpose_map = {
            'ZYX': (0, 1, 2),  # Ordre par défaut - pas de transposition
            'ZXY': (0, 2, 1),  # Z, X, Y
            'YZX': (1, 0, 2),  # Y, Z, X
            'YXZ': (1, 2, 0),  # Y, X, Z
            'XZY': (2, 0, 1),  # X, Z, Y
            'XYZ': (2, 1, 0),  # X, Y, Z
        }

        if self.axis_order in transpose_map:
            transpose_order = transpose_map[self.axis_order]
            if transpose_order == (0, 1, 2):
                return volume  # Pas de transposition nécessaire
            else:
                transposed_volume = np.transpose(volume, transpose_order)
                self.logger.info(f"[INFERENCE_TO_VOLUME] Axes réorganisés de {volume.shape} vers {transposed_volume.shape}")
                return transposed_volume
        else:
            self.logger.warning(f"[INFERENCE_TO_VOLUME] Ordre d'axes '{self.axis_order}' non supporté, utilisation de ZYX")
            self.logger.info(f"[INFERENCE_TO_VOLUME] Ordres supportés : {', '.join(transpose_map.keys())}")
            return volume

    def save_volume(self, volume):
        """Sauvegarde le volume dans le format spécifié"""
        if self.output_format == 'npz':
            self._save_npz(volume)
        elif self.output_format == 'h5':
            self._save_h5(volume)
        elif self.output_format == 'nifti':
            self._save_nifti(volume)
        else:
            raise ValueError(f"Format de sortie non supporté : {self.output_format}")

    def _save_npz(self, volume):
        """Sauvegarde au format NPZ avec métadonnées d'inférence"""
        output_file = self.output_path.with_suffix('.npz')

        # Créer un dictionnaire avec le volume et des métadonnées
        save_dict = {
            ARRAY_KEY_NAME: volume,
            'shape': volume.shape,
            'dtype': str(volume.dtype),
            'axis_order': self.axis_order,
            'unique_classes': np.unique(volume),
            'source_folder': str(self.inference_folder)
        }

        np.savez_compressed(output_file, **save_dict)
        self.logger.info(f"[INFERENCE_TO_VOLUME] ✅ Volume sauvegardé au format NPZ : {output_file}")
        self.logger.info(f"[INFERENCE_TO_VOLUME] Clé principale : '{ARRAY_KEY_NAME}'")

    def _save_h5(self, volume):
        """Sauvegarde au format H5 avec métadonnées"""
        try:
            import h5py
        except ImportError:
            raise ImportError("h5py n'est pas installé. Installez-le avec : pip install h5py")

        output_file = self.output_path.with_suffix('.h5')
        with h5py.File(output_file, 'w') as f:
            # Sauvegarder le volume principal
            dataset = f.create_dataset('inference_masks', data=volume, compression='gzip')
            
            # Ajouter des métadonnées comme attributs
            dataset.attrs['axis_order'] = self.axis_order
            dataset.attrs['source_folder'] = str(self.inference_folder)
            dataset.attrs['unique_classes'] = np.unique(volume)
            
        self.logger.info(f"[INFERENCE_TO_VOLUME] ✅ Volume sauvegardé au format H5 : {output_file}")

    def _save_nifti(self, volume):
        """Sauvegarde au format NIfTI pour usage médical"""
        try:
            import nibabel as nib
        except ImportError:
            raise ImportError("nibabel n'est pas installé. Installez-le avec : pip install nibabel")

        output_file = self.output_path.with_suffix('.nii.gz')
        
        # Créer une matrice d'affine simple (identité)
        affine = np.eye(4)
        
        # Créer l'image NIfTI
        nifti_img = nib.Nifti1Image(volume, affine=affine)
        
        # Sauvegarder
        nib.save(nifti_img, output_file)
        self.logger.info(f"[INFERENCE_TO_VOLUME] ✅ Volume sauvegardé au format NIfTI : {output_file}")


def convert_inference_to_volume(inference_folder, output_folder=None):
    """
    Fonction principale pour convertir les masques d'inférence en volume 3D.
    Utilise les paramètres de configuration hardcodés définis en haut du fichier.

    Args:
        inference_folder: Dossier contenant les masques d'inférence PNG
        output_folder: Dossier de sortie (optionnel, par défaut dans le dossier d'inférence)

    Returns:
        str: Chemin du fichier de volume créé, ou None si erreur
    """
    try:
        inference_path = Path(inference_folder)
        
        # Déterminer le dossier de sortie
        if output_folder is None:
            output_folder = inference_path.parent
        
        # Créer le nom de fichier de sortie basé sur le nom du dossier d'inférence
        folder_name = inference_path.name
        output_filename = f"{OUTPUT_PREFIX}_{folder_name}"
        output_path = Path(output_folder) / output_filename

        # Créer le dossier de sortie si nécessaire
        Path(output_folder).mkdir(parents=True, exist_ok=True)

        # Créer le convertisseur avec les paramètres de configuration
        converter = InferenceToVolumeConverter(
            inference_folder=inference_folder,
            output_path=output_path,
            output_format=OUTPUT_FORMAT,
            axis_order=AXIS_ORDER
        )

        # Effectuer la conversion
        success = converter.convert()

        if success:
            # Retourner le chemin du fichier créé
            extension_map = {'npz': '.npz', 'h5': '.h5', 'nifti': '.nii.gz'}
            final_path = output_path.with_suffix(extension_map[OUTPUT_FORMAT])
            return str(final_path)
        else:
            return None

    except Exception as e:
        print(f"[ERROR] Erreur lors de la conversion des masques d'inférence : {e}")
        logging.error(f"Erreur lors de la conversion inference vers volume : {e}")
        return None


def main():
    """Fonction principale avec arguments en ligne de commande"""
    parser = argparse.ArgumentParser(description="Convertit les masques d'inférence nnUNet en volume 3D")
    parser.add_argument("inference_folder", help="Dossier contenant les masques d'inférence PNG")
    parser.add_argument("output_path", help="Chemin de sortie (sans extension)")
    parser.add_argument("--format", choices=['npz', 'h5', 'nifti'], default='npz',
                       help="Format de sortie (défaut: npz)")
    parser.add_argument("--axis-order", choices=['ZYX', 'ZXY', 'YZX', 'YXZ', 'XZY', 'XYZ'], default='ZYX',
                       help="Ordre des axes - ZYX: (profondeur,hauteur,largeur) (défaut: ZYX)")

    args = parser.parse_args()

    try:
        # Créer le convertisseur
        converter = InferenceToVolumeConverter(
            inference_folder=args.inference_folder,
            output_path=args.output_path,
            output_format=args.format,
            axis_order=args.axis_order
        )

        # Effectuer la conversion
        converter.convert()

    except Exception as e:
        print(f"[ERROR] Erreur lors de la conversion : {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
