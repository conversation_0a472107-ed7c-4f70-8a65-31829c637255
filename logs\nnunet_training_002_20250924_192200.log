2025-09-24 19:22:00,211 - INFO - [START] Début de l'entraînement nnUNet
2025-09-24 19:22:00,211 - INFO - [CONFIG] Configuration:
2025-09-24 19:22:00,211 - INFO -    - Dataset ID: 002
2025-09-24 19:22:00,212 - INFO -    - Configuration: 2d
2025-09-24 19:22:00,212 - INFO -    - Époques: 1
2025-09-24 19:22:00,212 - INFO -    - Validation croisée: True
2025-09-24 19:22:00,212 - INFO -    - Prétraitement: True
2025-09-24 19:22:00,212 - INFO -    - GPU(s): 0
2025-09-24 19:22:00,212 - INFO -    - Trainer: nnUNetTrainer_1epoch
2025-09-24 19:22:00,212 - INFO - [PREPROCESSING] Lancement du prétraitement...
2025-09-24 19:22:00,213 - INFO - [START] Prétraitement et vérification du dataset: nnUNetv2_plan_and_preprocess -d 002 --verify_dataset_integrity -np 1
2025-09-24 19:22:24,425 - INFO - [SUCCESS] Succès: Prétraitement et vérification du dataset
2025-09-24 19:22:24,425 - INFO - [CV] Mode mixte - folds individuels: [0, 1, 2, 3, 4] + fold 'all'
2025-09-24 19:22:24,426 - INFO - [FOLD] Début du fold 0
2025-09-24 19:22:24,426 - INFO - [START] Entraînement fold 0: nnUNetv2_train 002 2d 0 -p nnUNetPlans -tr nnUNetTrainer_1epoch
2025-09-24 19:23:18,876 - INFO - [SUCCESS] Succès: Entraînement fold 0
2025-09-24 19:23:18,876 - INFO - [SUCCESS] Fold 0 terminé avec succès
2025-09-24 19:23:18,876 - INFO - [EXPORT] Lancement de l'export cumulatif après fold 0
2025-09-24 19:23:18,876 - INFO - [START] Export cumulatif des folds [0]: python export_model_zip.py --folds "0" --dataset-id 002 --configuration 2d --epochs 1 --plans-name nnUNetPlans
2025-09-24 19:23:36,266 - INFO - [SUCCESS] Succès: Export cumulatif des folds [0]
2025-09-24 19:23:36,266 - INFO - [EXPORT] Modèles folds [0] exportés avec succès
2025-09-24 19:23:36,268 - INFO - [FOLD] Début du fold 1
2025-09-24 19:23:36,268 - INFO - [START] Entraînement fold 1: nnUNetv2_train 002 2d 1 -p nnUNetPlans -tr nnUNetTrainer_1epoch
2025-09-24 19:24:36,698 - INFO - [SUCCESS] Succès: Entraînement fold 1
2025-09-24 19:24:36,698 - INFO - [SUCCESS] Fold 1 terminé avec succès
2025-09-24 19:24:36,698 - INFO - [EXPORT] Lancement de l'export cumulatif après fold 1
2025-09-24 19:24:36,698 - INFO - [START] Export cumulatif des folds [0,1]: python export_model_zip.py --folds "0 1" --dataset-id 002 --configuration 2d --epochs 1 --plans-name nnUNetPlans
2025-09-24 19:25:09,039 - INFO - [SUCCESS] Succès: Export cumulatif des folds [0,1]
2025-09-24 19:25:09,040 - INFO - [EXPORT] Modèles folds [0,1] exportés avec succès
2025-09-24 19:25:09,040 - INFO - [FOLD] Début du fold 2
2025-09-24 19:25:09,040 - INFO - [START] Entraînement fold 2: nnUNetv2_train 002 2d 2 -p nnUNetPlans -tr nnUNetTrainer_1epoch
2025-09-24 19:26:13,003 - INFO - [SUCCESS] Succès: Entraînement fold 2
2025-09-24 19:26:13,003 - INFO - [SUCCESS] Fold 2 terminé avec succès
2025-09-24 19:26:13,004 - INFO - [EXPORT] Lancement de l'export cumulatif après fold 2
2025-09-24 19:26:13,004 - INFO - [START] Export cumulatif des folds [0,1,2]: python export_model_zip.py --folds "0 1 2" --dataset-id 002 --configuration 2d --epochs 1 --plans-name nnUNetPlans
2025-09-24 19:27:08,352 - INFO - [SUCCESS] Succès: Export cumulatif des folds [0,1,2]
2025-09-24 19:27:08,352 - INFO - [EXPORT] Modèles folds [0,1,2] exportés avec succès
2025-09-24 19:27:08,352 - INFO - [FOLD] Début du fold 3
2025-09-24 19:27:08,353 - INFO - [START] Entraînement fold 3: nnUNetv2_train 002 2d 3 -p nnUNetPlans -tr nnUNetTrainer_1epoch
2025-09-24 19:28:25,717 - INFO - [SUCCESS] Succès: Entraînement fold 3
2025-09-24 19:28:25,729 - INFO - [SUCCESS] Fold 3 terminé avec succès
2025-09-24 19:28:25,740 - INFO - [EXPORT] Lancement de l'export cumulatif après fold 3
2025-09-24 19:28:25,749 - INFO - [START] Export cumulatif des folds [0,1,2,3]: python export_model_zip.py --folds "0 1 2 3" --dataset-id 002 --configuration 2d --epochs 1 --plans-name nnUNetPlans
2025-09-24 19:30:24,917 - INFO - [SUCCESS] Succès: Export cumulatif des folds [0,1,2,3]
2025-09-24 19:30:24,917 - INFO - [EXPORT] Modèles folds [0,1,2,3] exportés avec succès
2025-09-24 19:30:24,918 - INFO - [FOLD] Début du fold 4
2025-09-24 19:30:24,918 - INFO - [START] Entraînement fold 4: nnUNetv2_train 002 2d 4 -p nnUNetPlans -tr nnUNetTrainer_1epoch
2025-09-24 19:31:50,395 - INFO - [SUCCESS] Succès: Entraînement fold 4
2025-09-24 19:31:50,395 - INFO - [SUCCESS] Fold 4 terminé avec succès
2025-09-24 19:31:50,395 - INFO - [EXPORT] Lancement de l'export cumulatif après fold 4
2025-09-24 19:31:50,396 - INFO - [START] Export cumulatif des folds [0,1,2,3,4]: python export_model_zip.py --folds "0 1 2 3 4" --dataset-id 002 --configuration 2d --epochs 1 --plans-name nnUNetPlans
2025-09-24 19:33:11,596 - INFO - [SUCCESS] Succès: Export cumulatif des folds [0,1,2,3,4]
2025-09-24 19:33:11,597 - INFO - [EXPORT] Modèles folds [0,1,2,3,4] exportés avec succès
2025-09-24 19:33:11,597 - INFO - [FOLD] Début du fold all
2025-09-24 19:33:11,597 - INFO - [START] Entraînement fold all: nnUNetv2_train 002 2d all -p nnUNetPlans -tr nnUNetTrainer_1epoch
2025-09-24 19:34:26,177 - INFO - [SUCCESS] Succès: Entraînement fold all
2025-09-24 19:34:26,177 - INFO - [SUCCESS] Fold all terminé avec succès
2025-09-24 19:34:26,177 - INFO - [EXPORT] Lancement de l'export cumulatif après fold all
2025-09-24 19:34:26,178 - INFO - [START] Export cumulatif des folds [0,1,2,3,4,all]: python export_model_zip.py --folds "0 1 2 3 4 all" --dataset-id 002 --configuration 2d --epochs 1 --plans-name nnUNetPlans
2025-09-24 19:36:04,521 - INFO - [SUCCESS] Succès: Export cumulatif des folds [0,1,2,3,4,all]
2025-09-24 19:36:04,522 - INFO - [EXPORT] Modèles folds [0,1,2,3,4,all] exportés avec succès
2025-09-24 19:36:04,522 - INFO - [RESULTS] Résultats de l'entraînement:
2025-09-24 19:36:04,522 - INFO - [SUCCESS] Folds réussis: [0, 1, 2, 3, 4, 'all']
2025-09-24 19:36:04,523 - INFO - [INFO] Recherche de configuration ignorée (SAVE_NPZ désactivé)
2025-09-24 19:36:04,523 - INFO - [COMPLETE] Processus complet terminé en 0.23 heures
