# nnU-Net Pipeline

Pipeline complet pour l'entraînement et l'inférence avec nnU-Net, incluant des utilitaires pour la visualisation et l'analyse des résultats.

## Table des matières

- [Installation](#installation)
- [Configuration des chemins](#configuration-des-chemins)
- [Scripts principaux](#scripts-principaux)
- [Scripts utilitaires](#scripts-utilitaires)
- [Structure des dossiers](#structure-des-dossiers)
- [Utilisation](#utilisation)

## Installation

### 1. Installation de nnU-Net

> **Documentation officielle** : [Instructions d'installation nnU-Net](https://github.com/MIC-DKFZ/nnUNet/blob/master/documentation/installation_instructions.md)

```bash
# Créer un environnement conda
conda create -n nnunet_env python=3.10 -y
conda activate nnunet_env

# Installer PyTorch avec support CUDA
conda install pytorch torchvision torchaudio pytorch-cuda=11.8 -c pytorch -c nvidia

# Vérifier l'installation CUDA
python -c "import torch; print(torch.__version__); print(torch.cuda.is_available())"

# Installer nnU-Net
git clone https://github.com/MIC-DKFZ/nnUNet.git
cd nnUNet
pip install -e .
```

### 2. Installation des dépendances du pipeline

```bash
# Installer toutes les dépendances depuis le fichier requirements.txt
pip install -r requirements.txt
```

## Configuration des chemins

> **Documentation officielle** :
> - [Configuration des chemins](https://github.com/MIC-DKFZ/nnUNet/blob/master/documentation/setting_up_paths.md)
> - [Variables d'environnement](https://github.com/MIC-DKFZ/nnUNet/blob/master/documentation/set_environment_variables.md)


### Linux/Unix
Exécutez le script de configuration :
```bash
chmod +x new_path.sh
sudo ./new_path.sh
# Puis changez les permissions pour votre utilisateur :
sudo chown -R $USER:$USER /mnt/datasets/nnUnet/nnUnet_raw
sudo chown -R $USER:$USER /mnt/datasets/nnUnet/nnUnet_preprocessed
sudo chown -R $USER:$USER /mnt/results/nnUnet_results
```

### Windows
Avant d'exécuter la commande ci-dessous, ouvrez et modifiez le fichier `new_path_windows.ps1` afin d'y renseigner les chemins adaptés à votre machine (par exemple les dossiers `nnUNet_raw`, `nnUNet_preprocessed`, `nnUNet_results`).

Exécutez le script PowerShell :
```powershell
.\new_path_windows.ps1
```

## Convention de nommage des fichiers

> **Documentation officielle** :
> - [Format des datasets d'entraînement](https://github.com/MIC-DKFZ/nnUNet/blob/master/documentation/dataset_format.md)
> - [Format des datasets d'inférence](https://github.com/MIC-DKFZ/nnUNet/blob/master/documentation/dataset_format_inference.md)

nnU-Net nécessite une convention de nommage spécifique pour fonctionner correctement. Cette section explique les formats requis et comment utiliser les scripts de renommage automatique.

### Format requis pour l'entraînement

Pour l'entraînement, les fichiers doivent être organisés dans la structure nnU-Net avec des noms spécifiques :

**Images d'entraînement** (`imagesTr/`) :
- Format : `XXXX_0000.png`
- Exemple : `0001_0000.png`, `0002_0000.png`, `0003_0000.png`

**Labels d'entraînement** (`labelsTr/`) :
- Format : `XXXX.png`
- Exemple : `0001.png`, `0002.png`, `0003.png`

### Format requis pour l'inférence

Pour l'inférence, les images doivent suivre le même format que les images d'entraînement :
- Format : `XXXX_0000.png`
- Exemple : `0001_0000.png`, `0002_0000.png`, `0003_0000.png`

### Scripts de renommage automatique

Le pipeline inclut des scripts pour automatiser le renommage de vos fichiers :

#### `utils/rename_for_training.py`
**Fonction** : Renomme automatiquement les fichiers d'un dataset pour l'entraînement

**Utilisation** :
```bash
python utils/rename_for_training.py <dataset_id>
```

**Exemple** :
```bash
# Pour renommer les fichiers du Dataset011
python utils/rename_for_training.py 011
```

**Fonctionnement** :
- Trouve automatiquement le dataset dans `nnUNet_raw/`
- Renomme les images en `XXXX_0000.png` (ordre alphabétique)
- Renomme les labels en `XXXX.png` (même ordre)
- Vérifie la correspondance 1-à-1 entre images et labels

#### `utils/rename_for_inference.py`
**Fonction** : Renomme les fichiers d'un dossier pour l'inférence

**Configuration** :
Modifiez la variable `INFERENCE_FOLDER` dans le script :
```python
INFERENCE_FOLDER = r"C:\chemin\vers\votre\dossier"
```

**Utilisation** :
```bash
python utils/rename_for_inference.py
```

**Fonctionnement** :
- Renomme tous les fichiers images (.png, .jpg, .jpeg) du dossier
- Applique le format `XXXX_0000.png` (ordre alphabétique)
- Conserve l'ordre original des fichiers

### Conseils pratiques

1. **Avant l'entraînement** : Utilisez `rename_for_training.py` sur votre dataset
2. **Avant l'inférence** : Utilisez `rename_for_inference.py` sur votre dossier d'images
3. **Vérification** : Les scripts affichent un résumé des opérations effectuées
4. **Sauvegarde** : Faites une copie de vos fichiers originaux avant renommage

## Scripts principaux

### `train_nnunet.py`
**Fonction** : Entraînement complet d'un modèle nnU-Net avec validation croisée

**Caractéristiques** :
- Support multi-plateforme (Windows/Linux)
- Validation croisée 5-fold configurable
- Gestion automatique des trainers selon le nombre d'époques
- Logging détaillé avec horodatage
- Prétraitement automatique des données

**Configuration** :
```python
DATASET_ID = "011"           # ID du dataset
CONFIGURATION = "2d"         # Configuration nnU-Net
EPOCHS = 5                   # Nombre d'époques (voir valeurs disponibles ci-dessous)
USE_CROSS_VALIDATION = True  # Validation croisée
VALIDATION_FOLDS = [0, 1, 2, 3, 4, "all"]  # Folds à entraîner
```

**Important - Valeurs d'époques disponibles** :
Le nombre d'époques ne peut pas être arbitraire. Vous devez choisir parmi les trainers pré-définis :

**Tests rapides/debugging** :
- `1` → `nnUNetTrainer_1epoch`
- `5` → `nnUNetTrainer_5epochs`
- `10` → `nnUNetTrainer_10epochs`
- `20` → `nnUNetTrainer_20epochs`

**Entraînement court à moyen** :
- `50` → `nnUNetTrainer_50epochs`
- `100` → `nnUNetTrainer_100epochs`
- `250` → `nnUNetTrainer_250epochs`

**Entraînement long** :
- `500` → `nnUNetTrainer_500epochs`
- `750` → `nnUNetTrainer_750epochs`
- `2000` → `nnUNetTrainer_2000epochs`
- `4000` → `nnUNetTrainer_4000epochs`
- `8000` → `nnUNetTrainer_8000epochs`

**Note** : Le script sélectionne automatiquement le trainer correspondant. Si vous utilisez un nombre d'époques non supporté, l'entraînement échouera car le trainer n'existe pas.

**Utilisation** :
```bash
python train_nnunet.py
```

### `infer_nnunet.py`
**Fonction** : Inférence avec post-traitement automatique et visualisation

**Caractéristiques** :
- Inférence avec modèles de validation croisée ou modèle unique
- Post-traitement automatique des masques
- Création d'overlays avec contours
- Analyse des labels détectés
- Génération de vidéos à partir des overlays
- Validation automatique des noms de fichiers

**Utilisation** :
```bash
# Avec dossier par défaut
python infer_nnunet.py

# Avec dossier personnalisé
python infer_nnunet.py --input_folder "chemin/vers/images"
```

**Format requis** : Les images doivent être nommées `XXXX_0000.png` (ex: `0001_0000.png`)

**Configuration des époques** : La variable `EPOCHS` dans `infer_nnunet.py` doit correspondre exactement au nombre d'époques utilisé lors de l'entraînement. Utilisez les mêmes valeurs que celles listées dans la section `train_nnunet.py` (1, 5, 10, 20, 50, 100, 250, 500, 750, 2000, 4000, 8000).

### `run_batch_inference.py`
**Fonction** : Traitement par lot de plusieurs dossiers d'images

**Caractéristiques** :
- Traitement automatique de tous les sous-dossiers
- Validation des noms de fichiers (optionnelle)
- Gestion des erreurs avec continuation
- Logging détaillé pour chaque dossier
- Timeout de sécurité (2h par dossier)

**Configuration** :
```python
PARENT_FOLDER = r"C:\chemin\vers\dossier\parent"  # Dossier contenant les sous-dossiers
SKIP_VALIDATION = False      # Ignorer la validation des noms
CONTINUE_ON_ERROR = True     # Continuer malgré les erreurs
```

**Utilisation** :
```bash
python run_batch_inference.py
```

### `export_model_zip.py`
**Fonction** : Export des modèles entraînés au format ZIP

**Caractéristiques** :
- Export de folds individuels ou combinés
- Vérification automatique de la disponibilité des modèles
- Support des modes validation croisée et "all"
- Génération automatique du nom de fichier

**Configuration** :
```python
DATASET_ID = "011"
VALIDATION_FOLDS = ["all"]  # ou [0, 1, 2, 3, 4] ou [0, 1, "all"]
```

**Utilisation** :
```bash
python export_model_zip.py
```

## Scripts utilitaires

### `utils/rename_for_training.py`
Renomme les fichiers pour l'entraînement nnU-Net (format `XXXX_0000.png`)

### `utils/rename_for_inference.py`
Renomme les fichiers pour l'inférence nnU-Net

### `utils/visualisation.py`
Fonctions de reconversion et visualisation des masques prédits

### `utils/overlay_manager.py`
Gestion des overlays avec différents styles (contours, transparence)

### `utils/label_analyzer.py`
Analyse statistique des labels dans les masques de segmentation

### `utils/png_to_video.py`
Création de vidéos à partir de séquences d'images PNG

## Structure des dossiers

```
nnunet-pipeline/
├── train_nnunet.py           # Script d'entraînement
├── infer_nnunet.py           # Script d'inférence
├── run_batch_inference.py    # Traitement par lot
├── export_model_zip.py       # Export de modèles
├── new_path.sh              # Configuration Linux
├── new_path_windows.ps1     # Configuration Windows
├── utils/                   # Utilitaires
│   ├── rename_for_training.py
│   ├── rename_for_inference.py
│   ├── visualisation.py
│   ├── overlay_manager.py
│   ├── label_analyzer.py
│   └── png_to_video.py
└── logs/                    # Logs d'entraînement
```

## Utilisation

> **Documentation officielle** : [Guide d'utilisation nnU-Net](https://github.com/MIC-DKFZ/nnUNet/blob/master/documentation/how_to_use_nnunet.md)

### Workflow complet

1. **Configuration initiale** :
   ```bash
   # Linux
   sudo ./new_path.sh
   
   # Windows
   .\new_path_windows.ps1
   ```

2. **Entraînement** :
   ```bash
   python train_nnunet.py
   ```

3. **Inférence simple** :
   ```bash
   python infer_nnunet.py --input_folder "chemin/vers/images"
   ```

4. **Inférence par lot** :
   ```bash
   # Configurer PARENT_FOLDER dans le script
   python run_batch_inference.py
   ```

5. **Export du modèle** :
   ```bash
   python export_model_zip.py
   ```

### Résultats générés

- **Entraînement** : Modèles dans `nnUnet_results/`
- **Inférence** :
  - Masques bruts dans `BASE_OUTPUT_ROOT/nom_dossier_input/`
  - Masques reconvertis dans `BASE_OUTPUT_ROOT/nom_dossier_input/reconverted_masks/`
  - Overlays dans `BASE_OUTPUT_ROOT/nom_dossier_input/overlays/`
  - Vidéo dans `BASE_OUTPUT_ROOT/nom_dossier_input/overlays/`
  - Rapport d'analyse dans `BASE_OUTPUT_ROOT/nom_dossier_input/`

**Note sur BASE_OUTPUT_ROOT** : Cette variable est définie dans `infer_nnunet.py` et varie selon le système :
- **Windows** : `C:\Users\<USER>\Documents\results\inference`
- **Linux/Unix** : `/mnt/results/inference`

Le dossier de sortie final est créé automatiquement en combinant `BASE_OUTPUT_ROOT` avec le nom du dossier d'entrée. Si le dossier existe déjà, une version numérotée est créée (ex: `nom_dossier_v2`).

## Configuration avancée

Tous les scripts utilisent une détection automatique du système d'exploitation et adaptent les chemins en conséquence. Les paramètres principaux sont configurables en début de chaque script dans la section `=== CONFIGURATION ===`.
