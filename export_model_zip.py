import os
import subprocess
import logging
import platform
import argparse

# Configuration du logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# === CONFIGURATION (MODIFIEZ ICI) ===
DATASET_ID = "011"
CONFIGURATION = "2d"      # '2d', '3d_fullres', '3d_lowres'
PLANS_NAME = "nnUNetPlans"
EPOCHS = 5                # Doit correspondre au modèle entraîné

# === CONFIGURATION DES FOLDS ===
# Folds à exporter - peut inclure des nombres ET/OU "all"
# Exemples:
#   ["all"] = export seulement fold_all
#   [0, 1, 2, 3, 4] = export folds individuels 0-4
#   [0, 1, 2, 3, 4, "all"] = export folds 0-4 + fold_all
#   [2, 3, "all"] = export folds 2, 3 + fold_all
# Par défaut, sera remplacé par l'argument de ligne de commande si fourni
VALIDATION_FOLDS = [0]

# === DÉTECTION SYSTÈME ET PATHS ===
# Détection automatique du système d'exploitation
IS_WINDOWS = platform.system() == "Windows"

# Configuration des chemins selon le système
if IS_WINDOWS:
    # Chemins Windows
    RAW_PATH = r"C:\Users\<USER>\Documents\datasets\nnUnet\nnUnet_raw"
    PREPROCESSED_PATH = r"C:\Users\<USER>\Documents\datasets\nnUnet\nnUnet_preprocessed"
    RESULTS_PATH = r"C:\Users\<USER>\Documents\results\nnUnet_results"
    # Dossier de copie des modèles exportés (Windows)
    MODELS_COPY_PATH = r"C:\Users\<USER>\Documents\results\modèles Unet"
else:
    # Chemins Linux/Unix
    RAW_PATH = "/mnt/datasets/nnUnet/nnUnet_raw"
    PREPROCESSED_PATH = "/mnt/datasets/nnUnet/nnUnet_preprocessed"
    RESULTS_PATH = "/mnt/results/nnUnet_results"
    # Dossier de copie des modèles exportés (Linux)
    MODELS_COPY_PATH = "/mnt/results/nnUnet_results/modèles Unet"

# === CONFIGURATION ENVIRONNEMENT ===
os.environ["nnUNet_raw"] = RAW_PATH
os.environ["nnUNet_preprocessed"] = PREPROCESSED_PATH
os.environ["nnUNet_results"] = RESULTS_PATH

# === GESTION DES ARGUMENTS ===
def parse_arguments():
    """Parse les arguments de ligne de commande"""
    parser = argparse.ArgumentParser(description="Export d'un modèle nnUNet spécifique")
    parser.add_argument('--fold', type=str, help='Fold spécifique à exporter (ex: 0, 1, 2, 3, 4, ou "all")')
    parser.add_argument('--folds', type=str, help='Multiple folds à exporter séparés par des espaces (ex: "0 1 2" ou "0 1 all")')
    return parser.parse_args()

# Parse les arguments
args = parse_arguments()

# Gestion des arguments de folds
if args.folds is not None:
    # Multiple folds fournis via --folds
    fold_strings = args.folds.split()
    VALIDATION_FOLDS = []
    for fold_str in fold_strings:
        try:
            fold_value = int(fold_str)
        except ValueError:
            fold_value = fold_str  # Garder "all" comme string
        VALIDATION_FOLDS.append(fold_value)
    logger.info(f"[ARGS] Multiple folds demandés: {VALIDATION_FOLDS}")
elif args.fold is not None:
    # Un seul fold fourni via --fold
    try:
        fold_value = int(args.fold)
    except ValueError:
        fold_value = args.fold  # Garder "all" comme string

    VALIDATION_FOLDS = [fold_value]
    logger.info(f"[ARGS] Fold spécifique demandé: {args.fold}")
else:
    # Utilisation de la configuration par défaut
    logger.info(f"[ARGS] Utilisation de la configuration par défaut: {VALIDATION_FOLDS}")

# === RECHERCHE DU DATASET ===
def find_dataset_folder():
    """Trouve automatiquement le dossier du dataset"""
    dataset_pattern = f"Dataset{DATASET_ID}_"
    for item in os.listdir(RESULTS_PATH):
        if item.startswith(dataset_pattern):
            return item
    raise FileNotFoundError(f"Dataset {DATASET_ID} non trouvé dans {RESULTS_PATH}")

print("\n[INFO] Recherche du dataset...")
dataset_name = find_dataset_folder()
logger.info(f"[DATASET] Trouvé: {dataset_name}")

# === VÉRIFICATION DES MODÈLES DISPONIBLES ===
trainer_class = f"nnUNetTrainer_{EPOCHS}epochs"
trainer_path = os.path.join(RESULTS_PATH, dataset_name, f"{trainer_class}__{PLANS_NAME}__{CONFIGURATION}")

def check_model_availability():
    """Vérifie que les modèles requis sont disponibles avant l'export"""
    if not os.path.exists(trainer_path):
        raise FileNotFoundError(
            f"[ERROR] Dossier du trainer non trouvé: {trainer_path}\n"
            f"Vérifiez que l'entraînement a été effectué avec les bons paramètres:\n"
            f"  - DATASET_ID: {DATASET_ID}\n"
            f"  - EPOCHS: {EPOCHS}\n"
            f"  - CONFIGURATION: {CONFIGURATION}"
        )

    # Vérifier les folds demandés (peut inclure des nombres et "all")
    missing_folds = []
    for fold in VALIDATION_FOLDS:
        fold_path = os.path.join(trainer_path, f"fold_{fold}")
        checkpoint_path = os.path.join(fold_path, "checkpoint_final.pth")
        if not os.path.exists(checkpoint_path):
            missing_folds.append(fold)

    if missing_folds:
        available_folds = []
        for item in os.listdir(trainer_path):
            if item.startswith("fold_") and os.path.isdir(os.path.join(trainer_path, item)):
                checkpoint = os.path.join(trainer_path, item, "checkpoint_final.pth")
                if os.path.exists(checkpoint):
                    available_folds.append(item.replace("fold_", ""))

        raise FileNotFoundError(
            f"[ERROR] Modèles manquants:\n"
            f"  - Folds demandés: {VALIDATION_FOLDS}\n"
            f"  - Folds manquants: {missing_folds}\n"
            f"  - Folds disponibles: {available_folds}\n"
            f"Solution: Modifiez VALIDATION_FOLDS pour utiliser seulement les folds disponibles"
        )

    # Vérifier fold_all seulement si demandé
    if "all" in VALIDATION_FOLDS:
        fold_all_path = os.path.join(trainer_path, "fold_all")
        checkpoint_path = os.path.join(fold_all_path, "checkpoint_final.pth")

        if not os.path.exists(checkpoint_path):
            # Chercher les folds disponibles comme alternative
            available_folds = []
            for item in os.listdir(trainer_path):
                if item.startswith("fold_") and item != "fold_all" and os.path.isdir(os.path.join(trainer_path, item)):
                    checkpoint = os.path.join(trainer_path, item, "checkpoint_final.pth")
                    if os.path.exists(checkpoint):
                        available_folds.append(item.replace("fold_", ""))

            error_msg = (
                f"[ERROR] Modèle 'fold_all' non trouvé: {checkpoint_path}\n"
                f"Le dataset {dataset_name} n'a pas été entraîné en mode 'all'.\n"
            )

            if available_folds:
                error_msg += (
                    f"Solutions disponibles:\n"
                    f"  1. Utilisez la validation croisée:\n"
                    f"     USE_CROSS_VALIDATION = True\n"
                    f"     VALIDATION_FOLDS = {available_folds}\n"
                    f"  2. Entraînez un modèle en mode 'all':\n"
                    f"     Dans train_nnunet.py: USE_CROSS_VALIDATION = False"
                )
            else:
                error_msg += (
                    f"Aucun modèle trouvé. Vérifiez que l'entraînement s'est terminé correctement."
                )

            raise FileNotFoundError(error_msg)

# === FONCTION DE COPIE ===
def copy_model_to_destination(zip_file_path):
    """Copie le modèle exporté vers le dossier de destination selon l'OS"""
    try:
        # Créer le dossier de destination s'il n'existe pas
        os.makedirs(MODELS_COPY_PATH, exist_ok=True)
        logger.info(f"[COPY] Dossier de destination: {MODELS_COPY_PATH}")

        # Chemin de destination complet
        destination_file = os.path.join(MODELS_COPY_PATH, os.path.basename(zip_file_path))

        # Commande de copie selon l'OS
        if IS_WINDOWS:
            # Windows: utiliser copy
            copy_cmd = f'copy "{zip_file_path}" "{destination_file}"'
        else:
            # Linux/Unix: utiliser cp
            copy_cmd = f'cp "{zip_file_path}" "{destination_file}"'

        logger.info(f"[COPY] Commande de copie: {copy_cmd}")

        # Exécuter la commande de copie
        subprocess.run(copy_cmd, shell=True, check=True, capture_output=True, text=True)

        # Vérifier que le fichier a bien été copié
        if os.path.exists(destination_file):
            file_size = os.path.getsize(destination_file) / (1024 * 1024)  # MB
            logger.info(f"[COPY] ✅ Copie réussie vers: {destination_file}")
            logger.info(f"[COPY] Taille du fichier copié: {file_size:.1f} MB")
            print(f"\n✅ Modèle copié vers: {destination_file}")
            return True
        else:
            logger.error(f"[COPY] ❌ Fichier de destination non trouvé après copie")
            return False

    except subprocess.CalledProcessError as e:
        logger.error(f"[COPY] ❌ Erreur lors de la copie: {e}")
        print(f"\n❌ Échec de la copie vers {MODELS_COPY_PATH}")
        print(f"Erreur: {e}")
        return False
    except Exception as e:
        logger.error(f"[COPY] ❌ Erreur inattendue lors de la copie: {e}")
        print(f"\n❌ Erreur inattendue lors de la copie: {e}")
        return False

# Vérifier la disponibilité des modèles
logger.info(f"[CHECK] Vérification des modèles disponibles...")
check_model_availability()
logger.info(f"[CHECK] ✅ Modèles disponibles et compatibles")

# === CONSTRUCTION DE LA COMMANDE ===
# Construire la chaîne des folds pour la commande
folds_str = " ".join(map(str, VALIDATION_FOLDS))

# Construire le suffixe pour le nom de fichier
folds_suffix = f"folds{'_'.join(map(str, VALIDATION_FOLDS))}"

# Affichage informatif selon le contenu de VALIDATION_FOLDS
if len(VALIDATION_FOLDS) == 1 and VALIDATION_FOLDS[0] == "all":
    print(f"[INFO] Mode 'all' - export du modèle entraîné sur toutes les données")
elif "all" in VALIDATION_FOLDS:
    numeric_folds = [f for f in VALIDATION_FOLDS if f != "all"]
    print(f"[INFO] Mode mixte - export des folds individuels: {numeric_folds} + fold 'all'")
else:
    print(f"[INFO] Mode validation croisée - export des folds: {VALIDATION_FOLDS}")

OUTPUT_ZIP = f"model_{DATASET_ID}_{CONFIGURATION}_{folds_suffix}.zip"

cmd = (
    f"nnUNetv2_export_model_to_zip "
    f"-d {dataset_name} "
    f"-o {OUTPUT_ZIP} "
    f"-c {CONFIGURATION} "
    f"-tr {trainer_class} "
    f"-p {PLANS_NAME} "
    f"-f {folds_str}"
)

# === AFFICHAGE ET EXÉCUTION ===
print("Commande générée :")
print(cmd)
print(f"\nFichier de sortie : {OUTPUT_ZIP}")
print("\nExportation en cours...\n")

try:
    subprocess.run(cmd, shell=True, check=True)
    print(f"\nExportation réussie : {OUTPUT_ZIP}")

    # Vérifier la taille du fichier
    if os.path.exists(OUTPUT_ZIP):
        file_size = os.path.getsize(OUTPUT_ZIP) / (1024 * 1024)  # MB
        print(f"Taille du fichier : {file_size:.1f} MB")

        # Copier le modèle vers le dossier de destination
        logger.info(f"[COPY] Début de la copie du modèle exporté")
        copy_success = copy_model_to_destination(OUTPUT_ZIP)

        if not copy_success:
            logger.warning(f"[COPY] La copie a échoué, mais l'export principal a réussi")

except subprocess.CalledProcessError as e:
    print(f"\nÉchec de l'exportation.")
    print(f"Erreur : {e}")
    print(f"Code de retour : {e.returncode}")



print(f"\nPour installer sur un autre système :")
print(f"nnUNetv2_install_pretrained_model_from_zip {OUTPUT_ZIP}")
